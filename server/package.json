{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js", "seed": "node seed.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "openai": "^4.93.0", "express-rate-limit": "^7.2.0"}, "devDependencies": {"nodemon": "^3.1.9"}}