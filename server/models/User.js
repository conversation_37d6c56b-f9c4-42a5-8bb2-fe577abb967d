const mongoose = require("mongoose");
const bcrypt = require("bcrypt");

// User schema definition
const UserSchema = new mongoose.Schema({
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    minlength: [3, 'Username must be at least 3 characters'],
    maxlength: [30, 'Username cannot exceed 30 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters']
  },
  firstName: {
    type: String,
    trim: true,
    maxlength: [50, 'First name too long']
  },
  lastName: {
    type: String,
    trim: true,
    maxlength: [50, 'Last name too long']
  },
  bio: {
    type: String,
    default: "",
    maxlength: [500, 'Bio cannot exceed 500 characters']
  },
  profilePicture: {
    type: String,
    default: ""
  },
  isAdmin: {
    type: Boolean,
    default: false
  },
  lastLogin: {
    type: Date,
    default: Date.now
  },
  // TODO: Add email verification fields
  // emailVerified: { type: Boolean, default: false },
  // verificationToken: String,

  // TODO: Add user preferences
  // preferences: {
  //   notifications: { type: Boolean, default: true },
  //   theme: { type: String, default: 'light' }
  // }
}, {
  timestamps: true // This adds createdAt and updatedAt automatically
});

// Pre-save middleware to hash passwords
UserSchema.pre("save", async function (next) {
  // Only hash if password is new or modified
  if (!this.isModified("password")) {
    return next();
  }

  try {
    // Generate salt and hash password
    const saltRounds = 12; // increased from 10 for better security
    this.password = await bcrypt.hash(this.password, saltRounds);
    next();
  } catch (error) {
    next(error);
  }
});

// Instance method to verify password
UserSchema.methods.comparePassword = async function (candidatePassword) {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    throw new Error('Password comparison failed');
  }
};

// Check admin status
UserSchema.methods.isAdminUser = function () {
  return this.isAdmin === true;
};

// Get user's full name
UserSchema.methods.getFullName = function () {
  if (this.firstName && this.lastName) {
    return `${this.firstName} ${this.lastName}`;
  }
  return this.firstName || this.lastName || this.username;
};

// Update last login timestamp
UserSchema.methods.updateLastLogin = async function () {
  this.lastLogin = new Date();
  return this.save({ validateBeforeSave: false }); // Skip validation for this update
};

// Create indexes for better query performance
UserSchema.index({ email: 1 });
UserSchema.index({ username: 1 });
UserSchema.index({ createdAt: -1 });

module.exports = mongoose.model("User", UserSchema);
