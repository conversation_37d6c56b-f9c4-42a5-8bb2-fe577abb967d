const OpenAI = require("openai");

// Global state for OpenAI client
let openaiClient = null;
let lastRequestTime = 0;
const REQUEST_COOLDOWN = 1000; // 1 second between requests to avoid rate limits

// Initialize the OpenAI client
const initializeOpenAI = () => {
  try {
    const apiKey = process.env.OPENAI_API_KEY;

    if (!apiKey || apiKey === 'your-api-key-here') {
      console.warn("⚠️  OpenAI API key not configured");
      return false;
    }

    openaiClient = new OpenAI({
      apiKey: apiKey.trim(),
      maxRetries: 2, // reduced retries to fail faster
      timeout: 20000, // 20 second timeout
    });

    console.log("🤖 OpenAI client initialized");
    return true;
  } catch (error) {
    console.error("❌ Failed to initialize OpenAI:", error.message);
    return false;
  }
};

// Test if the API key is valid
const validateApiKey = async () => {
  if (!openaiClient) {
    if (!initializeOpenAI()) {
      return false;
    }
  }

  try {
    // Simple test request to validate the key
    const testResponse = await openaiClient.chat.completions.create({
      messages: [{ role: "user", content: "hi" }],
      model: process.env.OPENAI_MODEL || "gpt-3.5-turbo",
      max_tokens: 5,
    });

    if (testResponse?.choices?.[0]) {
      console.log("✅ OpenAI API key validated");
      return true;
    }
    return false;
  } catch (error) {
    if (error.status === 429) {
      console.warn("⚠️  OpenAI rate limit hit during validation");
    } else if (error.status === 401) {
      console.error("❌ Invalid OpenAI API key");
    } else {
      console.error("❌ OpenAI validation failed:", error.message);
    }
    return false;
  }
};

exports.validateApiKey = validateApiKey;

// Fallback responses when AI is unavailable
const fallbackResponses = [
  "I'm here to listen. Tell me more about what's on your mind.",
  "That sounds really tough. Have you thought about reaching out to a counselor?",
  "I hear you. Remember to be kind to yourself during difficult times.",
  "Let's think about what small steps you could take today. What feels manageable?",
  "Thanks for sharing that with me. What kind of support would be most helpful right now?",
  "I'm experiencing some technical issues, but I'm still here to listen.",
  "Sometimes it helps to just talk things through. What's been weighing on you?",
  "That's a lot to handle. Have you been able to talk to anyone else about this?",
];

// Simple rate limiting to avoid hitting API limits
const waitForCooldown = () => {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < REQUEST_COOLDOWN) {
    const waitTime = REQUEST_COOLDOWN - timeSinceLastRequest;
    return new Promise(resolve => setTimeout(resolve, waitTime));
  }

  lastRequestTime = now;
  return Promise.resolve();
};

// Main function to generate AI responses
const generateResponse = async (userMessage) => {
  // Initialize client if needed
  if (!openaiClient && !initializeOpenAI()) {
    console.log("Using fallback - AI client not available");
    return getRandomFallback();
  }

  try {
    await waitForCooldown();

    const response = await openaiClient.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You're a supportive mental health companion. Be empathetic and encouraging, but never give medical advice. Keep responses under 150 words. If someone seems in crisis, gently suggest professional help."
        },
        {
          role: "user",
          content: userMessage
        }
      ],
      max_tokens: 120,
      temperature: 0.8, // slightly more creative
      presence_penalty: 0.1 // encourage variety
    });

    const aiResponse = response.choices[0]?.message?.content?.trim();

    if (!aiResponse) {
      console.warn("Empty response from OpenAI");
      return getRandomFallback();
    }

    return aiResponse;

  } catch (error) {
    console.error("AI generation error:", error.message);

    // Handle different error types
    if (error.status === 429) {
      console.log("Rate limited - using fallback");
    } else if (error.status === 401) {
      console.log("Auth error - using fallback");
    }

    return getRandomFallback();
  }
};

exports.generateResponse = generateResponse;

// Get a random fallback response
const getRandomFallback = () => {
  const randomIndex = Math.floor(Math.random() * fallbackResponses.length);
  return fallbackResponses[randomIndex];
};
