const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const auth = require('../middleware/auth');

// User registration
router.post('/register', authController.register);

// User login
router.post('/login', authController.login);

// Get current user data (protected)
router.get('/user', auth, authController.getCurrentUser);

// Get any user's public profile (protected)
router.get('/user/:id', auth, authController.getUserProfile);

module.exports = router;
