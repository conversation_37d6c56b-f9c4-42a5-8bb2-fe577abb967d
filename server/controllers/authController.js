const User = require('../models/User');
const jwt = require('jsonwebtoken');

// User registration handler
const register = async (req, res) => {
  try {
    const { username, email, password, firstName, lastName } = req.body;

    // Check for existing users - email first since it's more common
    const existingEmail = await User.findOne({ email: email.toLowerCase() });
    if (existingEmail) {
      return res.status(400).json({
        error: 'Email already registered',
        field: 'email'
      });
    }

    // Then check username
    const existingUsername = await User.findOne({ username });
    if (existingUsername) {
      return res.status(400).json({
        error: 'Username not available',
        field: 'username'
      });
    }

    // Create the user
    const newUser = new User({
      username: username.trim(),
      email: email.toLowerCase().trim(),
      password,
      firstName: firstName?.trim(),
      lastName: lastName?.trim()
    });

    await newUser.save();
    console.log(`New user registered: ${username} (${email})`);

    // TODO: Send welcome email
    // TODO: Add user to default groups/preferences

    // Generate JWT - keeping it simple
    const token = jwt.sign(
      { userId: newUser._id },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.status(201).json({
      token,
      user: {
        id: newUser._id,
        username: newUser.username,
        email: newUser.email,
        firstName: newUser.firstName,
        lastName: newUser.lastName
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Something went wrong during registration' });
  }
};

exports.register = register;

// Login handler
const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user by email (case insensitive)
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(401).json({ error: 'Invalid email or password' });
    }

    // Verify password
    const isValidPassword = await user.comparePassword(password);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid email or password' });
    }

    // Update last login timestamp
    user.lastLogin = new Date();
    await user.save();

    // Create token
    const token = jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    console.log(`User logged in: ${user.username}`);

    res.json({
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        lastLogin: user.lastLogin
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
};

exports.login = login;

// Get current authenticated user
const getCurrentUser = async (req, res) => {
  try {
    // req.user should be set by auth middleware
    const user = await User.findById(req.user.userId).select('-password');

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Get current user error:', error);
    res.status(500).json({ error: 'Failed to fetch user data' });
  }
};

// Get any user's public profile
const getUserProfile = async (req, res) => {
  try {
    const { id } = req.params;

    // Only return public fields for user profiles
    const user = await User.findById(id).select('username firstName lastName bio profilePicture createdAt');

    if (!user) {
      return res.status(404).json({ error: 'User profile not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Get user profile error:', error);

    // Handle invalid ObjectId
    if (error.name === 'CastError') {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    res.status(500).json({ error: 'Failed to fetch user profile' });
  }
};

exports.getCurrentUser = getCurrentUser;
exports.getUserProfile = getUserProfile;
