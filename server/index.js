const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const dotenv = require("dotenv");
const path = require("path");
const { validateApiKey } = require("./services/aiService");

// Load env vars first thing
dotenv.config();

const app = express();

// Basic middleware setup
app.use(cors());
app.use(express.json({ limit: '10mb' })); // had issues with large payloads before

// TODO: add rate limiting middleware here
// TODO: maybe add helmet for security headers?

// Database connection - tried a few different approaches here
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || "mongodb://localhost:27017/mental-health-app";
    await mongoose.connect(mongoURI);
    console.log("✅ MongoDB connected successfully");
  } catch (error) {
    console.error("❌ MongoDB connection failed:", error.message);
    // Don't exit process in dev, just log the error
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
  }
};

// Initialize AI service (this can fail gracefully)
const initAI = async () => {
  try {
    const isValid = await validateApiKey();
    if (!isValid) {
      console.warn("⚠️  OpenAI API key invalid - AI features disabled");
    } else {
      console.log("🤖 AI service ready");
    }
  } catch (err) {
    console.warn("⚠️  AI service initialization failed:", err.message);
  }
};

// Start everything up
connectDB();
initAI();

// Route imports - organized by feature
const authRoutes = require("./routes/auth");
const moodRoutes = require("./routes/mood");
const journalRoutes = require("./routes/journal");
const goalsRoutes = require("./routes/goals");
const exercisesRoutes = require("./routes/exercises");
const resourcesRoutes = require("./routes/resources");
const postsRoutes = require("./routes/posts");
const aiRoutes = require("./routes/ai");

// API routes setup
app.use("/api/auth", authRoutes);
app.use("/api/mood", moodRoutes); // keeping singular for now, might change later
app.use("/api/journal", journalRoutes);
app.use("/api/goals", goalsRoutes);
app.use("/api/exercises", exercisesRoutes);
app.use("/api/resources", resourcesRoutes);
app.use("/api/posts", postsRoutes); // TODO: rename to /api/community for consistency
app.use("/api/ai", aiRoutes);

// TODO: Add API versioning (v1, v2) when we have breaking changes
// TODO: Add request logging middleware
// TODO: Add API documentation endpoint

// Production static file serving
if (process.env.NODE_ENV === "production") {
  // Serve the built React app
  app.use(express.static(path.join(__dirname, "../client/dist"))); // Vite builds to dist, not build

  // Catch all handler for SPA routing
  app.get("*", (req, res) => {
    res.sendFile(path.resolve(__dirname, "../client/dist", "index.html"));
  });
}

// Health check endpoint - useful for deployment
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`🚀 Server is running on port ${PORT}`);
  console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
});
